import { stringToStream, stringToWordStream, stringToTypewriterStream } from './string-to-stream';

// Example usage of the string-to-stream utilities

// Example 1: Basic word-by-word streaming (matches existing codebase pattern)
const completeText = "This is a complete response that needs to be streamed word by word.";
const wordStream = stringToWordStream(completeText);

// Example 2: Custom streaming options
const customStream = stringToStream(completeText, {
    initialDelayInMs: 100,    // Wait 100ms before starting
    chunkDelayInMs: 25,       // 25ms between chunks
    chunkBy: 'word'           // Stream word by word
});

// Example 3: Character-by-character typewriter effect
const typewriterStream = stringToTypewriterStream(completeText, 30); // 30ms per character

// Example 4: Sentence-by-sentence streaming
const sentenceStream = stringToStream(completeText, {
    chunkBy: 'sentence',
    chunkDelayInMs: 500       // 500ms between sentences
});

// Example 5: Line-by-line streaming for multi-line content
const multiLineText = `Line 1 of the response
Line 2 of the response
Line 3 of the response`;

const lineStream = stringToStream(multiLineText, {
    chunkBy: 'line',
    chunkDelayInMs: 200       // 200ms between lines
});

// Usage in your completion function (replace existing streamText call):
// Instead of: response = streamText(completionQry);
// Use: response = stringToWordStream(yourCompleteString);

export {
    wordStream,
    customStream,
    typewriterStream,
    sentenceStream,
    lineStream
};
