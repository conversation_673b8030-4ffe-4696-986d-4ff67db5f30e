import { simulateReadableStream } from 'ai';

/**
 * Converts a complete string into the same streaming format that streamText returns
 * @param {string} text - The complete text to convert to a stream
 * @param {Object} options - Configuration options
 * @param {number} options.initialDelayInMs - Initial delay before streaming starts (default: 0)
 * @param {number} options.chunkDelayInMs - Delay between chunks in milliseconds (default: 10)
 * @param {string} options.chunkBy - How to split the text: 'word', 'sentence', 'character', or 'line' (default: 'word')
 * @param {number} options.chunkSize - For character chunking, how many characters per chunk (default: 1)
 * @returns {Object} - Object with stream property compatible with streamText return format
 */
export function stringToStream(text, options = {}) {
    const {
        initialDelayInMs = 0,
        chunkDelayInMs = 10,
        chunkBy = 'word',
        chunkSize = 1
    } = options;

    let chunks;
    
    switch (chunkBy) {
        case 'word':
            chunks = text.split(/\s+/).filter(word => word.length > 0).map(word => ({ 
                type: 'text-delta', 
                text: word + ' ' 
            }));
            break;
            
        case 'sentence':
            chunks = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0).map(sentence => ({ 
                type: 'text-delta', 
                text: sentence.trim() + '. ' 
            }));
            break;
            
        case 'line':
            chunks = text.split(/\n/).map(line => ({ 
                type: 'text-delta', 
                text: line + '\n' 
            }));
            break;
            
        case 'character':
            chunks = [];
            for (let i = 0; i < text.length; i += chunkSize) {
                chunks.push({ 
                    type: 'text-delta', 
                    text: text.slice(i, i + chunkSize) 
                });
            }
            break;
            
        default:
            throw new Error(`Invalid chunkBy option: ${chunkBy}. Use 'word', 'sentence', 'character', or 'line'.`);
    }

    return {
        stream: simulateReadableStream({
            initialDelayInMs,
            chunkDelayInMs,
            chunks,
        }),
    };
}

/**
 * Convenience function that matches the exact pattern used in the codebase
 * Splits by words with 10ms delay between chunks
 * @param {string} text - The complete text to convert to a stream
 * @returns {Object} - Object with stream property compatible with streamText return format
 */
export function stringToWordStream(text) {
    return stringToStream(text, { chunkBy: 'word', chunkDelayInMs: 10 });
}

/**
 * Creates a faster character-by-character stream for typewriter effect
 * @param {string} text - The complete text to convert to a stream
 * @param {number} delayMs - Delay between characters in milliseconds (default: 50)
 * @returns {Object} - Object with stream property compatible with streamText return format
 */
export function stringToTypewriterStream(text, delayMs = 50) {
    return stringToStream(text, { 
        chunkBy: 'character', 
        chunkDelayInMs: delayMs,
        chunkSize: 1 
    });
}
